import 'dart:async';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../providers/device_provider.dart';
import '../providers/auth_provider.dart';
import '../services/anti_lost_service.dart';
import '../utils/app_logger.dart';

class AntiLostMonitorScreen extends StatefulWidget {
  const AntiLostMonitorScreen({Key? key}) : super(key: key);

  @override
  State<AntiLostMonitorScreen> createState() => _AntiLostMonitorScreenState();
}

class _AntiLostMonitorScreenState extends State<AntiLostMonitorScreen>
    with WidgetsBindingObserver {
  // 防丢服务
  late AntiLostService _antiLostService;

  // 计时器，用于周期性检查
  Timer? _periodicCheckTimer;

  @override
  void initState() {
    super.initState();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      // 获取AntiLostService实例
      _antiLostService = Provider.of<AntiLostService>(context, listen: false);
      // 初始化防丢服务
      _initServices();
      // 启动周期性检查
      _startPeriodicCheck();
    });
  }

  // 初始化所有服务
  Future<void> _initServices() async {
    try {
      await _antiLostService.init();
    } catch (e, stackTrace) {
      AppLogger.error('初始化服务失败：$e', error: e, stackTrace: stackTrace);
    }
  }

  // 启动周期性检查 - 每分钟检查一次
  void _startPeriodicCheck() {
    _periodicCheckTimer?.cancel();
    _periodicCheckTimer = Timer.periodic(
      const Duration(seconds: 40),
      (timer) async {
        final deviceProvider =
            Provider.of<DeviceProvider>(context, listen: false);
        // 查询设备运行状态
        await deviceProvider.updateRunningStatus();
        final device = deviceProvider.device;
        // 检查应用是否后台
        bool isAppInBackground =
            WidgetsBinding.instance.lifecycleState == AppLifecycleState.paused;
        if (device != null &&
            !device.isSleep &&
            device.antiLostMode &&
            !device.isOnline &&
            isAppInBackground) {
          _antiLostService.checkDeviceConnection(device, alarmCallback: () {
            _antiLostService.showAlertDialog(device);
          });
        }
      },
    );
  }

  @override
  void dispose() {
    // 取消周期性检查
    _periodicCheckTimer?.cancel();

    // 释放防丢服务资源
    _antiLostService.dispose();

    // 移除应用生命周期观察者
    WidgetsBinding.instance.removeObserver(this);

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // 使用Selector监听设备在线状态和防丢模式状态
    return Consumer<DeviceProvider>(
      builder: (context, provider, child) {
        final device = provider.device;

        // 当设备在线且开启防丢模式时，检查设备连接
        if (device != null &&
            !device.isSleep &&
            device.antiLostMode &&
            !device.isOnline) {
          // 使用Future.microtask确保在build完成后执行
          Future.microtask(() {
            _antiLostService.checkDeviceConnection(device, alarmCallback: () {
              // 获取根 Navigator 的 context
              _antiLostService.showAlertDialog(device);
            });
          });
        }
        // 此Widget不展示任何UI，仅在后台监控
        return const SizedBox.shrink();
      },
    );
  }
}

// 用于将AntiLostMonitorScreen集成到应用中的包装器
class AntiLostMonitorWidget extends StatelessWidget {
  final Widget child;

  const AntiLostMonitorWidget({
    Key? key,
    required this.child,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // 使用 Consumer 监听 AuthProvider 的登录状态
    return Consumer<AuthProvider>(
      builder: (context, authProvider, _) {
        // 只有在用户登录时才启用防丢监控
        if (authProvider.isLogin) {
          // 将AntiLostMonitorScreen叠加在应用的其他UI上
          return Stack(
            children: [
              child, // 应用的主UI
              const AntiLostMonitorScreen(), // 防丢监控界面（不可见）
            ],
          );
        } else {
          // 用户未登录时，只显示主UI，不启用防丢监控
          return child;
        }
      },
    );
  }
}
